package com.stpl.tech.attendance.service.RosteringService.impl;

import com.stpl.tech.attendance.cache.service.UnitCacheService;
import com.stpl.tech.attendance.cache.service.UserCacheService;
import com.stpl.tech.attendance.domain.EmpShiftMappingFinder;
import com.stpl.tech.attendance.domain.EmpShiftMappingList;
import com.stpl.tech.attendance.dto.EmployeeMetadataDTO;
import com.stpl.tech.attendance.dto.RosteringDto.CafeLiveDashboardDTO;
import com.stpl.tech.attendance.dto.RosteringDto.CafeLiveDashboardResponseDTO;
import com.stpl.tech.attendance.dto.RosteringDto.CafeShiftDataDTO;
import com.stpl.tech.attendance.dto.RosteringDto.EmpShiftMappingDTO;
import com.stpl.tech.attendance.dto.RosteringDto.EmpShiftUpdateRequestDTO;
import com.stpl.tech.attendance.dto.RosteringDto.EmployeeShiftDataResponseDTO;
import com.stpl.tech.attendance.dto.RosteringDto.HierarchyEmployeeDTO;
import com.stpl.tech.attendance.dto.RosteringDto.ShiftCafeMappingDTO;
import com.stpl.tech.attendance.dto.RosteringDto.ShiftEmployeesDTO;
import com.stpl.tech.attendance.dto.RosteringDto.ShiftEmployeesResponseDTO;
import com.stpl.tech.attendance.dto.RosteringDto.ShiftRequestDTO;
import com.stpl.tech.attendance.dto.RosteringDto.ShiftResponseDTO;
import com.stpl.tech.attendance.dto.RosteringDto.RosteringMetadataResponseDTO;
import com.stpl.tech.attendance.entity.RosteringEntity.EmpShiftMapping;
import com.stpl.tech.attendance.entity.RosteringEntity.RosteringConstants;
import com.stpl.tech.attendance.entity.RosteringEntity.Shift;
import com.stpl.tech.attendance.entity.RosteringEntity.ShiftCafeMapping;
import com.stpl.tech.attendance.repository.RosteringRepository.EmpShiftMappingRepository;
import com.stpl.tech.attendance.repository.RosteringRepository.ShiftCafeMappingRepository;
import com.stpl.tech.attendance.repository.RosteringRepository.ShiftRepository;
import com.stpl.tech.attendance.service.MetadataService;
import com.stpl.tech.attendance.service.RosteringService.RosteringService;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class HierarchyEmployeeListResponseDTO {
    private List<HierarchyEmployeeListResponseDTO.EmployeeDTO> employees;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EmployeeDTO {
        private String employeeId;
        private String name;
        private String email;
        private String role;
    }
}

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class RosteringServiceImpl implements RosteringService {

    @Autowired
    private MetadataService metadataService;

    private final ShiftRepository shiftRepository;

    private final EmpShiftMappingRepository empShiftMappingRepository;
    private final ShiftCafeMappingRepository shiftCafeMappingRepository;
    private final UserCacheService userCacheService;
    private final UnitCacheService unitCacheService;

    @Override
    @Transactional(readOnly = true)
    public RosteringMetadataResponseDTO getRosteringMetadata(Integer employeeId, Integer unitId) {
        log.info("Getting rostering metadata for employeeId: {}, unitId: {}", employeeId, unitId);

        try {
            // Get employee details to check permissions
            EmployeeBasicDetail employee = userCacheService.getUserById(employeeId);
            if (employee == null) {
                log.warn("Employee with ID {} not found", employeeId);
                return buildDefaultMetadata();
            }

            // Get unit details
            UnitBasicDetail unit = unitCacheService.getUnitBasicDetail(unitId);
            if (unit == null) {
                log.warn("Unit with ID {} not found", unitId);
                return buildDefaultMetadata();
            }

            // Check if employee has any shift mappings (indicates shift management access)
            List<EmpShiftMapping> empShiftMappings = empShiftMappingRepository
                .findByEmpIdAndStatus(employeeId, RosteringConstants.SHIFT_STATUS_ACTIVE);
            boolean hasShiftMappings = !empShiftMappings.isEmpty();

            // Check if unit has any shift mappings
            List<ShiftCafeMapping> unitShiftMappings = shiftCafeMappingRepository
                .findByUnitIdAndStatus(unitId, RosteringConstants.SHIFT_STATUS_ACTIVE);
            boolean hasUnitShiftMappings = !unitShiftMappings.isEmpty();

            // Build metadata response based on permissions and data availability
            return RosteringMetadataResponseDTO.builder()
                .allShiftManagement(hasShiftMappings) // Enable if employee has shift mappings
                .unitShiftManagement(hasUnitShiftMappings) // Enable if unit has shift mappings
                .liveDashboardView(true) // Always enable live dashboard view
                .shiftDashboardView(hasUnitShiftMappings) // Enable if unit has shift mappings
                .employeeDashboardView(hasShiftMappings) // Enable if employee has shift mappings
                .build();

        } catch (Exception e) {
            log.error("Error getting rostering metadata for employeeId: {}, unitId: {}", employeeId, unitId, e);
            return buildDefaultMetadata();
        }
    }

    @Override
    @Transactional(readOnly = true)
    public CafeLiveDashboardResponseDTO getCafeLiveDashboard(Integer employeeId) {
        log.info("Getting cafe live dashboard data for employeeId: {}", employeeId);

        try {
            // Get employee details
            EmployeeBasicDetail employee = userCacheService.getUserById(employeeId);

            // Get employee's shift mappings
            List<EmpShiftMapping> empShiftMappings = empShiftMappingRepository
                .findByEmpIdAndStatus(employeeId, RosteringConstants.SHIFT_STATUS_ACTIVE);

            // Get shifts and build response
            List<CafeLiveDashboardResponseDTO.ShiftInfoDTO> shifts = new ArrayList<>();
            int totalActual = 0;

            for (EmpShiftMapping mapping : empShiftMappings) {
                Shift shift = shiftRepository.findById(mapping.getShiftId()).orElse(null);
                if (shift != null) {
                    // Get employee count for this shift
                    List<Integer> empIds = empShiftMappingRepository
                        .findEmpIdsByShiftIdAndStatus(shift.getShiftId(), RosteringConstants.SHIFT_STATUS_ACTIVE);

                    CafeLiveDashboardResponseDTO.ShiftInfoDTO shiftInfo =
                        CafeLiveDashboardResponseDTO.ShiftInfoDTO.builder()
                            .shiftId(shift.getShiftId())
                            .startDate(shift.getStartTime())
                            .endDate(shift.getEndTime())
                            .numberOfEmployees(empIds.size())
                            .shiftName(shift.getShiftName())
                            .build();
                    shifts.add(shiftInfo);
                    totalActual += empIds.size();
                }
            }
            // Build dashboard view
            CafeLiveDashboardResponseDTO.DashboardViewDTO dashboardView =
                CafeLiveDashboardResponseDTO.DashboardViewDTO.builder()
                    .cafeDashboardView(CafeLiveDashboardResponseDTO.CafeDashboardViewDTO.builder()
                        .cafeDashboard(true)
                        .shiftDashboard(true)
                        .employeeDashboard(false)
                        .build())
                    .date(CafeLiveDashboardResponseDTO.DateRangeDTO.builder()
                            //todo
                        .startDate(shifts.get(0).getStartDate())
                        .endDate(shifts.get(shifts.size() - 1).getEndDate())
                        .build())
                    .cafeDashboard(CafeLiveDashboardResponseDTO.CafeDashboardStatsDTO.builder()
                        .actual(totalActual)
                        .ideal(totalActual + 30) // Example calculation
                        .build())
                    .shifts(shifts)
                    .build();

            return CafeLiveDashboardResponseDTO.builder()
                .dashboardView(dashboardView)
                .build();

        } catch (Exception e) {
            log.error("Error getting cafe live dashboard data", e);
            throw new RuntimeException("Failed to get cafe live dashboard data", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public ShiftEmployeesResponseDTO getShiftEmployeesForUser(List<Integer> shiftIds, Timestamp date, Integer userId) {
        log.info("Getting shift employees for shiftIds: {}, date: {}, userId: {}", shiftIds, date, userId);
        //userId = 140199;

        // Get employees under the given userId hierarchy
        List<EmployeeMetadataDTO> employees = metadataService.getEmployeeHierarchy(userId);
        log.info("Found {} employees under userId {} hierarchy", employees.size(), userId);

        // Create a set of employee IDs for efficient lookup
        Set<Integer> userEmployeeIds = employees.stream()
            .map(EmployeeMetadataDTO::getEmpId)
            .collect(Collectors.toSet());

        try {
            List<ShiftEmployeesResponseDTO.ShiftDTO> shiftList = new ArrayList<>();

            // Process each requested shift
            for (Integer shiftId : shiftIds) {
                // Fetch shift details
                Shift shift = shiftRepository.findById(shiftId).orElse(null);
                if (shift == null) {
                    log.warn("Shift with ID {} not found, skipping", shiftId);
                    continue;
                }

                // Fetch employee shift mappings for this shift where processingTo is infinity (current active mappings)
                // Fetch employee shift mappings for this shift
                List<EmpShiftMapping> mappings = empShiftMappingRepository.findByShiftIdAndStatus(shiftId, RosteringConstants.SHIFT_STATUS_ACTIVE);
                log.info("Found {} shift mappings for shiftId {}", mappings.size(), shiftId);

                // Collect employees for this shift
                List<ShiftEmployeesResponseDTO.EmployeeDTO> shiftEmployees = new ArrayList<>();

                // Process each mapping to find employees in hierarchy with shift assignments
                for (EmpShiftMapping mapping : mappings) {
                    // Check if this employee is under the given userId hierarchy
                    if (!userEmployeeIds.contains(mapping.getEmpId())) {
                        continue; // Skip employees not under the user's hierarchy
                    }

                    // Get employee details
                    EmployeeBasicDetail emp = userCacheService.getUserById(mapping.getEmpId());
                    if (emp != null) {
                        shiftEmployees.add(
                            ShiftEmployeesResponseDTO.EmployeeDTO.builder()
                                .employeeId("emp_" + String.format("%03d", emp.getId()))
                                .name(emp.getName())
                                .build()
                        );
                        log.debug("Added employee {} (ID: {}) to shift {} response", emp.getName(), emp.getId(), shiftId);
                    } else {
                        log.warn("Employee with ID {} not found in cache", mapping.getEmpId());
                    }
                }

                // Add shift with its employees to the response
                if (!shiftEmployees.isEmpty()) {
                    shiftList.add(
                        ShiftEmployeesResponseDTO.ShiftDTO.builder()
                            .shiftId(shift.getShiftId())
                            .employees(shiftEmployees)
                            .build()
                    );
                }
            }

            log.info("Returning {} shifts with employee assignments from hierarchy", shiftList.size());
            return ShiftEmployeesResponseDTO.builder()
                .shifts(shiftList)
                .build();

        } catch (Exception e) {
            log.error("Error getting shift employees for userId: {}, shiftIds: {}", userId, shiftIds, e);
            throw new RuntimeException("Failed to get shift employees", e);
        }
    }

@Override
@Transactional(readOnly = true)
public EmployeeShiftDataResponseDTO getEmpUpcomingShiftData(Integer empId, Timestamp startDate, Timestamp endDate) {
    log.info("Getting employee shift data for empId: {}, startDate: {}, endDate: {}", empId, startDate, endDate);

    try {
        // Get current date
        LocalDate today = LocalDate.now();
        log.info("Today's date: {}", today);

        // Create business date for asOf query - using today's date
        Timestamp businessAsOfDate = Timestamp.valueOf(today.atStartOfDay());

        // For processing date, use current timestamp to get latest processed records
        Timestamp processingAsOfDate = new Timestamp(System.currentTimeMillis());

        // Fetch all mappings for employee using Reladomo asOf processing date
        com.stpl.tech.attendance.domain.EmpShiftMappingList allMappings =
                com.stpl.tech.attendance.domain.EmpShiftMappingFinder.findMany(
                        com.stpl.tech.attendance.domain.EmpShiftMappingFinder.empId().eq(empId)
                                .and(com.stpl.tech.attendance.domain.EmpShiftMappingFinder.businessDate().eq(businessAsOfDate))
                                .and(com.stpl.tech.attendance.domain.EmpShiftMappingFinder.processingDate().asOf(processingAsOfDate))
                );

        log.info("Found {} active shift mappings for employee {}", allMappings.size(), empId);

        List<EmployeeShiftDataResponseDTO.EmployeeShiftDTO> shifts = new ArrayList<>();

        for (com.stpl.tech.attendance.domain.EmpShiftMapping mapping : allMappings) {
            Shift shift = shiftRepository.findById(mapping.getShiftId()).orElse(null);
            if (shift == null) {
                log.warn("Shift not found for shiftId: {}", mapping.getShiftId());
                continue;
            }

            LocalDate businessFrom = mapping.getExpectedStartDate() != null
                    ? mapping.getExpectedStartDate().toLocalDateTime().toLocalDate()
                    : null;
            LocalDate businessTo = mapping.getExpectedEndDate() != null
                    ? mapping.getExpectedEndDate().toLocalDateTime().toLocalDate()
                    : null;

            if (businessFrom == null || businessTo == null) {
                log.warn("Expected dates are null for mapping ID: {}", mapping.getId());
                continue;
            }

            // Skip past dates
            if (businessFrom.isBefore(today)) {
                businessFrom = today;
            }

            for (LocalDate date = businessFrom; !date.isAfter(businessTo); date = date.plusDays(1)) {
                LocalDateTime shiftStartDateTime = date.atTime(shift.getStartTime().toLocalTime());
                LocalDateTime shiftEndDateTime = date.atTime(shift.getEndTime().toLocalTime());

                EmployeeShiftDataResponseDTO.EmployeeShiftDTO shiftDto =
                        EmployeeShiftDataResponseDTO.EmployeeShiftDTO.builder()
                                .shiftId(shift.getShiftId())
                                .shiftName(shift.getShiftName())
                                .startTime(shiftStartDateTime)
                                .endTime(shiftEndDateTime)
                                .date(shiftStartDateTime)
                                .build();

                shifts.add(shiftDto);
                log.debug("Added shift {} for employee {} on {}", shift.getShiftId(), empId, date);
            }
        }

        log.info("Returning {} shifts for employee {}", shifts.size(), empId);
        return EmployeeShiftDataResponseDTO.builder()
                .employeeId(empId)
                .shifts(shifts)
                .build();

    } catch (Exception e) {
        log.error("Error getting employee shift data for empId: {}", empId, e);
        throw new RuntimeException("Failed to get employee shift data", e);
    }
}



//    @Override
//    public EmpShiftMappingDTO updateEmpShift(EmpShiftUpdateRequestDTO request) {
//        log.warn("updateEmpShift method is deprecated. Use ReladomoEmpShiftService.updateEmpShifts() for bulk updates.");
//
//        // For backward compatibility, handle single update
//        if (request.getEmpId() != null && request.getShiftId() != null) {
//
//            // Create a single mapping update
//            EmpShiftMapping mapping = EmpShiftMapping.builder()
//                .empId(request.getEmpId())
//                .shiftId(request.getShiftId())
//                .expectedStartDate(request.getExpectedArrivalTime() != null ? Timestamp.valueOf(request.getExpectedArrivalTime()) : null) // Convert to Timestamp
//                .expectedEndDate(request.getBusinessTo() != null ? Timestamp.valueOf(request.getBusinessTo()) : null) // Convert to Timestamp
//                .processingFrom(Timestamp.valueOf(LocalDateTime.now()))
//                .processingTo(Timestamp.valueOf(LocalDateTime.of(9999, 12, 31, 23, 59, 59)))
//                .businessFrom(request.getBusinessFrom() != null ? Timestamp.valueOf(request.getBusinessFrom()) : null) // Convert to Timestamp
//                .businessTo(request.getBusinessTo() != null ? Timestamp.valueOf(request.getBusinessTo()) : null) // Convert to Timestamp
//                .status(RosteringConstants.SHIFT_STATUS_ACTIVE)
//                .createdBy(request.getUpdatedBy())
//                .updatedBy(request.getUpdatedBy())
//                .build();
//
//            mapping = empShiftMappingRepository.save(mapping);
//            return convertToEmpShiftMappingDTO(mapping);
//        } else {
//            throw new UnsupportedOperationException("Employee ID and Shift ID are required");
//        }
//    }

    @Override
    @Transactional(readOnly = true)
    public List<CafeShiftDataDTO> getCafeShiftData(Integer employeeId, Integer unitId) {
        log.info("Getting cafe shift data for employeeId: {}, unitId: {}", employeeId, unitId);
        try {
            // Validate unitId is provided
            if (unitId == null) {
                log.warn("UnitId is null, returning empty result");
                return new ArrayList<>();
            }

            // Get unit details
            UnitBasicDetail unit = unitCacheService.getUnitBasicDetail(unitId);
            if (unit == null) {
                log.warn("Unit with ID {} not found", unitId);
                return new ArrayList<>();
            }

            // Get all active shift mappings for this unit
            List<ShiftCafeMapping> shiftCafeMappings = shiftCafeMappingRepository.findByUnitIdAndStatus(unitId, RosteringConstants.SHIFT_STATUS_ACTIVE);
            if (shiftCafeMappings == null || shiftCafeMappings.isEmpty()) {
                log.info("No shift mappings found for unitId: {}", unitId);
                return new ArrayList<>();
            }

            // Collect all unique shiftIds for this unit
            List<Integer> shiftIds = shiftCafeMappings.stream()
                    .map(ShiftCafeMapping::getShiftId)
                    .distinct()
                    .toList();

            // Build shift details for this unit
            List<CafeShiftDataDTO.ShiftDetailDTO> shiftDetails = new ArrayList<>();
            for (Integer shiftId : shiftIds) {
                Shift shift = shiftRepository.findById(shiftId).orElse(null);
                if (shift == null) {
                    log.warn("Shift with ID {} not found, skipping", shiftId);
                    continue;
                }

                // Get employee count for this shift
                List<Integer> empIds = empShiftMappingRepository.findEmpIdsByShiftIdAndStatus(shiftId, RosteringConstants.SHIFT_STATUS_ACTIVE);

                shiftDetails.add(
                    CafeShiftDataDTO.ShiftDetailDTO.builder()
                        .shiftId(shift.getShiftId())
                        .shiftName(shift.getShiftName())
                        .startTime(shift.getStartTime())
                        .endTime(shift.getEndTime())
                        .creationTime(shift.getCreationTime())
                        .createdBy(shift.getCreatedBy())
                        .build()
                );
            }

            // Build the result for this specific unit
            List<CafeShiftDataDTO> result = new ArrayList<>();
            result.add(
                CafeShiftDataDTO.builder()
                    .unitId(unit.getId())
                    .unitName(unit.getName())
                    .shifts(shiftDetails)
                    .build()
            );

            log.info("Returning cafe shift data for unitId: {} with {} shifts", unitId, shiftDetails.size());
            return result;

        } catch (Exception e) {
            log.error("Error getting cafe shift data for unitId: {}", unitId, e);
            throw new RuntimeException("Failed to get cafe shift data", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<HierarchyEmployeeDTO> getHierarchyEmployees(Integer employeeId) {
        log.info("Getting direct subordinates for employeeId: {}", employeeId);
        try {
            Map<Integer, EmployeeBasicDetail> allEmployees = userCacheService.getAllUserCache();
            List<HierarchyEmployeeDTO> result = new ArrayList<>();
            for (EmployeeBasicDetail emp : allEmployees.values()) {
                if (employeeId != null && employeeId.equals(emp.getReportingManagerId())) {
                    result.add(HierarchyEmployeeDTO.builder()
                        .empId(emp.getId())
                        .empName(emp.getName())
                        .emailId(emp.getEmailId())
                        .designation(emp.getDesignation())
                        .build());
                }
            }
            return result;
        } catch (Exception e) {
            log.error("Error getting direct subordinates for employeeId: {}", employeeId, e);
            throw new RuntimeException("Failed to get hierarchy employees", e);
        }
    }

    @NotNull
    private static List<Map<String, Object>> getEmpList(Integer employeeId, Map<Integer, EmployeeBasicDetail> allEmployees) {
        List<Map<String, Object>> result = new ArrayList<>();
        for (EmployeeBasicDetail emp : allEmployees.values()) {
            if (employeeId != null && employeeId.equals(emp.getReportingManagerId())) {
                Map<String, Object> empMap = new java.util.HashMap<>();
                empMap.put("employeeId", String.format("emp_%03d", emp.getId()));
                empMap.put("name", emp.getName());
                empMap.put("email", emp.getEmailId());
                empMap.put("role", emp.getDesignation());
                result.add(empMap);
            }
        }
        return result;
    }

    // Additional utility methods implementation

    @Override
    public ShiftResponseDTO createShift(ShiftRequestDTO shiftRequestDTO, String createdBy) {
        log.info("Creating new shift: {}", shiftRequestDTO.getShiftName());
        if(shiftRequestDTO.getStartTime().isAfter(shiftRequestDTO.getEndTime())){
            throw new RuntimeException("Start time cannot be after end time");
        }
        // validate shift_name,startTime,endTime form db
        if(shiftRepository.existsByShiftNameIgnoreCaseAndStartTimeAndEndTimeAndStatus(shiftRequestDTO.getShiftName(),shiftRequestDTO.getStartTime(),shiftRequestDTO.getEndTime())){
            throw new RuntimeException("Shift with name startTime and endTime '" + shiftRequestDTO.getShiftName() + "' and startTime '" + shiftRequestDTO.getStartTime() + "' and endTime '" + shiftRequestDTO.getEndTime() + "' already exists");
        }
        try{
            Shift shift = Shift.builder()
                    .shiftName(shiftRequestDTO.getShiftName())
                    .startTime(shiftRequestDTO.getStartTime())
                    .endTime(shiftRequestDTO.getEndTime())
                    .status(RosteringConstants.SHIFT_STATUS_ACTIVE)
                    .createdBy(createdBy)
                    .creationTime(LocalDateTime.now())
                    .updatedBy(createdBy)
                    .updationTime(LocalDateTime.now())
                    .build();
             shiftRepository.save(shift);
            return convertToShiftResponseDTO(shift);
        } catch (Exception e) {
            log.error("Error creating shift", e);
            throw new RuntimeException("Failed to create shift", e);
        }
    }

    @Override
    public ShiftResponseDTO updateShift(Integer shiftId, ShiftRequestDTO shiftRequestDTO, String updatedBy) {
        log.info("Updating shift with ID: {}", shiftId);

        try {
            com.stpl.tech.attendance.entity.RosteringEntity.Shift shift = shiftRepository.findById(shiftId)
                .orElseThrow(() -> new RuntimeException("Shift not found"));

            // Check if shift name already exists (excluding current shift)
            if (shiftRepository.existsByShiftNameIgnoreCaseAndShiftIdNotAndStatus(
                shiftRequestDTO.getShiftName(), shiftId)) {
                throw new RuntimeException("Shift with name '" + shiftRequestDTO.getShiftName() + "' already exists");
            }

            shift.setShiftName(shiftRequestDTO.getShiftName());
            shift.setStartTime(shiftRequestDTO.getStartTime());
            shift.setEndTime(shiftRequestDTO.getEndTime());
            shift.setUpdatedBy(updatedBy); // From HttpServletRequest

            shift = shiftRepository.save(shift);
            return convertToShiftResponseDTO(shift);

        } catch (Exception e) {
            log.error("Error updating shift", e);
            throw new RuntimeException("Failed to update shift", e);
        }
    }

    @Override
    public void deleteShift(Integer shiftId) {
        log.info("Deleting shift with ID: {}", shiftId);

        try {
            com.stpl.tech.attendance.entity.RosteringEntity.Shift shift = shiftRepository.findById(shiftId)
                .orElseThrow(() -> new RuntimeException("Shift not found"));

            shift.setStatus(RosteringConstants.SHIFT_STATUS_INACTIVE);
            shiftRepository.save(shift);

        } catch (Exception e) {
            log.error("Error deleting shift", e);
            throw new RuntimeException("Failed to delete shift", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<ShiftResponseDTO> getAllShifts(String status) {
        log.info("Getting all shifts with status: {}", status);

        try {
            List<com.stpl.tech.attendance.entity.RosteringEntity.Shift> shifts = status != null ?
                shiftRepository.findByStatus(status) :
                shiftRepository.findAll();

            return shifts.stream()
                .map(this::convertToShiftResponseDTO)
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Error getting all shifts", e);
            throw new RuntimeException("Failed to get all shifts", e);
        }
    }

    @Override
    public ShiftCafeMappingDTO createShiftCafeMapping(Integer shiftId, Integer unitId, String createdBy) {
        log.info("Creating shift cafe mapping for shiftId: {}, unitId: {}", shiftId, unitId);

        try {
            // Check if mapping already exists
            if (shiftCafeMappingRepository.existsByShiftIdAndUnitIdAndStatus(shiftId, unitId, RosteringConstants.SHIFT_STATUS_ACTIVE)) {
                throw new RuntimeException("Shift cafe mapping already exists");
            }

            ShiftCafeMapping mapping = ShiftCafeMapping.builder()
                .shiftId(shiftId)
                .unitId(unitId)
                .status(RosteringConstants.SHIFT_STATUS_ACTIVE)
                .createdBy(createdBy)
                .updatedBy(createdBy)
                .build();

            mapping = shiftCafeMappingRepository.save(mapping);
            return convertToShiftCafeMappingDTO(mapping);

        } catch (Exception e) {
            log.error("Error creating shift cafe mapping", e);
            throw new RuntimeException("Failed to create shift cafe mapping", e);
        }
    }

    @Override
    public void deleteShiftCafeMapping(Integer shiftId, Integer unitId) {
        log.info("Deleting shift cafe mapping for shiftId: {}, unitId: {}", shiftId, unitId);

        try {
            ShiftCafeMapping mapping = shiftCafeMappingRepository
                .findByShiftIdAndUnitIdAndStatus(shiftId, unitId, RosteringConstants.SHIFT_STATUS_ACTIVE)
                .orElseThrow(() -> new RuntimeException("Shift cafe mapping not found"));

            mapping.setStatus(RosteringConstants.SHIFT_STATUS_INACTIVE);
            shiftCafeMappingRepository.save(mapping);

        } catch (Exception e) {
            log.error("Error deleting shift cafe mapping", e);
            throw new RuntimeException("Failed to delete shift cafe mapping", e);
        }
    }

    @Override
    public EmpShiftMappingDTO createEmpShiftMapping(EmpShiftUpdateRequestDTO request) {
        log.info("Creating employee shift mapping: {}", request);

        try {
            // Get empId and shiftId from request
            Integer empId = request.getEmpId();
            Integer shiftId = request.getShiftId();

            if (empId == null || shiftId == null) {
                throw new IllegalArgumentException("Employee ID and Shift ID are required");
            }

            // Check for overlapping mappings
            List<EmpShiftMapping> overlapping = empShiftMappingRepository.findOverlappingMappings(
                empId, RosteringConstants.SHIFT_STATUS_ACTIVE, -1, request.getExpectedArrivalTime(), request.getBusinessTo());

            if (!overlapping.isEmpty()) {
                throw new RuntimeException("Employee already has overlapping shift mapping");
            }

            EmpShiftMapping mapping = EmpShiftMapping.builder()
                .empId(empId)
                .shiftId(shiftId)
                .expectedStartDate(request.getExpectedArrivalTime() != null ? Timestamp.valueOf(request.getExpectedArrivalTime()) : null) // Convert to Timestamp
                .expectedEndDate(request.getBusinessTo() != null ? Timestamp.valueOf(request.getBusinessTo()) : null) // Convert to Timestamp
                .processingFrom(Timestamp.valueOf(LocalDateTime.now()))
                .processingTo(Timestamp.valueOf(LocalDateTime.of(9999, 12, 31, 23, 59, 59)))
                .businessFrom(request.getBusinessFrom() != null ? Timestamp.valueOf(request.getBusinessFrom()) : null) // Convert to Timestamp
                .businessTo(request.getBusinessTo() != null ? Timestamp.valueOf(request.getBusinessTo()) : null) // Convert to Timestamp
                .status(RosteringConstants.SHIFT_STATUS_ACTIVE)
                .createdBy(request.getUpdatedBy())
                .updatedBy(request.getUpdatedBy())
                .build();

            mapping = empShiftMappingRepository.save(mapping);
            return convertToEmpShiftMappingDTO(mapping);

        } catch (Exception e) {
            log.error("Error creating employee shift mapping", e);
            throw new RuntimeException("Failed to create employee shift mapping", e);
        }
    }

    @Override
    public void deleteEmpShiftMapping(Integer mappingId) {
        log.info("Deleting employee shift mapping with ID: {}", mappingId);

        try {
            EmpShiftMapping mapping = empShiftMappingRepository.findById(mappingId)
                .orElseThrow(() -> new RuntimeException("Employee shift mapping not found"));

            mapping.setStatus(RosteringConstants.SHIFT_STATUS_INACTIVE);
            empShiftMappingRepository.save(mapping);

        } catch (Exception e) {
            log.error("Error deleting employee shift mapping", e);
            throw new RuntimeException("Failed to delete employee shift mapping", e);
        }
    }

    // Private helper methods

    private CafeLiveDashboardDTO buildCafeDashboard(UnitBasicDetail unit) {
        // Get shifts for this unit
        List<ShiftCafeMapping> shiftMappings = shiftCafeMappingRepository
            .findByUnitIdAndStatusWithShift(unit.getId(), RosteringConstants.SHIFT_STATUS_ACTIVE);

        List<CafeLiveDashboardDTO.ShiftSummaryDTO> shiftSummaries = shiftMappings.stream()
            .map(mapping -> {
                com.stpl.tech.attendance.entity.RosteringEntity.Shift shift = mapping.getShift();
                List<Integer> empIds = empShiftMappingRepository
                    .findEmpIdsByShiftIdAndStatus(shift.getShiftId(), RosteringConstants.SHIFT_STATUS_ACTIVE);

                return CafeLiveDashboardDTO.ShiftSummaryDTO.builder()
                    .shiftId(shift.getShiftId())
                    .shiftName(shift.getShiftName())
                    .shiftStartTime(shift.getStartTime())
                    .shiftEndTime(shift.getEndTime())
                    .totalEmployeesInShift(empIds.size())
                    .presentEmployeesInShift(0) // TODO: Calculate from attendance data
                    .absentEmployeesInShift(empIds.size()) // TODO: Calculate from attendance data
                    .build();
            })
            .collect(Collectors.toList());

        int totalEmployees = shiftSummaries.stream()
            .mapToInt(CafeLiveDashboardDTO.ShiftSummaryDTO::getTotalEmployeesInShift)
            .sum();

        return CafeLiveDashboardDTO.builder()
            .unitId(unit.getId())
            .unitName(unit.getName())
            .unitCode(unit.getReferenceName())
            .totalEmployees(totalEmployees)
            .presentEmployees(0) // TODO: Calculate from attendance data
            .absentEmployees(totalEmployees) // TODO: Calculate from attendance data
            .onBreakEmployees(0) // TODO: Calculate from attendance data
            .shiftSummaries(shiftSummaries)
            .lastUpdated(LocalDateTime.now())
            .build();
    }

    private ShiftEmployeesDTO buildShiftEmployees(com.stpl.tech.attendance.entity.RosteringEntity.Shift shift) {
        List<Integer> empIds = empShiftMappingRepository
            .findEmpIdsByShiftIdAndStatus(shift.getShiftId(), RosteringConstants.SHIFT_STATUS_ACTIVE);

        List<ShiftEmployeesDTO.EmployeeShiftDetailDTO> employees = empIds.stream()
            .map(empId -> {
                EmployeeBasicDetail emp = userCacheService.getUserById(empId);
                if (emp != null) {
                    return ShiftEmployeesDTO.EmployeeShiftDetailDTO.builder()
                        .empId(emp.getId())
                        .empName(emp.getName())
                        .empCode(emp.getEmployeeCode())
                        .designation(emp.getDesignation())
                        .contactNumber(emp.getContactNumber())
                        .status(emp.getStatus().name())
                        .attendanceStatus("UNKNOWN") // TODO: Calculate from attendance data
                        .build();
                }
                return null;
            })
            .filter(emp -> emp != null)
            .collect(Collectors.toList());

        return ShiftEmployeesDTO.builder()
            .shiftId(shift.getShiftId())
            .shiftName(shift.getShiftName())
            .shiftStartTime(shift.getStartTime())
            .shiftEndTime(shift.getEndTime())
            .totalEmployees(employees.size())
            .employees(employees)
            .build();
    }

    private CafeShiftDataDTO buildCafeShiftData(UnitBasicDetail unit) {
        List<ShiftCafeMapping> shiftMappings = shiftCafeMappingRepository
            .findByUnitIdAndStatusWithShift(unit.getId(), RosteringConstants.SHIFT_STATUS_ACTIVE);

        List<CafeShiftDataDTO.ShiftDetailDTO> shifts = shiftMappings.stream()
            .map(mapping -> {
                com.stpl.tech.attendance.entity.RosteringEntity.Shift shift = mapping.getShift();
                List<Integer> empIds = empShiftMappingRepository
                    .findEmpIdsByShiftIdAndStatus(shift.getShiftId(), RosteringConstants.SHIFT_STATUS_ACTIVE);

                return CafeShiftDataDTO.ShiftDetailDTO.builder()
                    .shiftId(shift.getShiftId())
                    .shiftName(shift.getShiftName())
                    .startTime(shift.getStartTime())
                    .endTime(shift.getEndTime())
                    .creationTime(shift.getCreationTime())
                    .createdBy(shift.getCreatedBy())
                    .build();
            })
            .collect(Collectors.toList());

        return CafeShiftDataDTO.builder()
            .unitId(unit.getId())
            .unitName(unit.getName())
            .shifts(shifts)
            .build();
    }

    private Shift convertToShiftDTO(com.stpl.tech.attendance.entity.RosteringEntity.Shift shift) {
        return Shift.builder()
            .shiftId(shift.getShiftId())
            .shiftName(shift.getShiftName())
            .startTime(shift.getStartTime())
            .endTime(shift.getEndTime())
            .status(shift.getStatus())
            .createdBy(shift.getCreatedBy())
            .creationTime(shift.getCreationTime())
            .updatedBy(shift.getUpdatedBy())
            .updationTime(shift.getUpdationTime())
            .build();
    }

    private ShiftResponseDTO convertToShiftResponseDTO(com.stpl.tech.attendance.entity.RosteringEntity.Shift shift) {
        return ShiftResponseDTO.builder()
            .shiftId(shift.getShiftId()) // Auto-generated from DB
            .shiftName(shift.getShiftName())
            .startTime(shift.getStartTime())
            .endTime(shift.getEndTime())
            .status(shift.getStatus())
//            .createdBy(shift.getCreatedBy()) // Set from HttpServletRequest
//            .creationTime(shift.getCreationTime()) // Set from current system time
//            .updatedBy(shift.getUpdatedBy()) // Set from HttpServletRequest
//            .updationTime(shift.getUpdationTime()) // Set from current system time
            .build();
    }

    private ShiftCafeMappingDTO convertToShiftCafeMappingDTO(ShiftCafeMapping mapping) {
        String shiftName = null;
        String unitName = null;

        if (mapping.getShift() != null) {
            shiftName = mapping.getShift().getShiftName();
        }

        UnitBasicDetail unit = unitCacheService.getUnitBasicDetail(mapping.getUnitId());
        if (unit != null) {
            unitName = unit.getName();
        }

        return ShiftCafeMappingDTO.builder()
            .shiftCafeMappingId(mapping.getShiftCafeMappingId())
            .shiftId(mapping.getShiftId())
            .shiftName(shiftName)
            .unitId(mapping.getUnitId())
            .unitName(unitName)
            .status(mapping.getStatus())
            .createdBy(mapping.getCreatedBy())
            .creationTime(mapping.getCreationTime())
            .updatedBy(mapping.getUpdatedBy())
            .updationTime(mapping.getUpdationTime())
            .build();
    }

    private EmpShiftMappingDTO convertToEmpShiftMappingDTO(EmpShiftMapping mapping) {
        String shiftName = null;
        String empName = null;
        String empCode = null;

        if (mapping.getShift() != null) {
            shiftName = mapping.getShift().getShiftName();
        }

        EmployeeBasicDetail emp = userCacheService.getUserById(mapping.getEmpId());
        if (emp != null) {
            empName = emp.getName();
            empCode = emp.getEmployeeCode();
        }

        return EmpShiftMappingDTO.builder()
            .id(mapping.getId())
            .shiftId(mapping.getShiftId())
            .shiftName(shiftName)
            .empId(mapping.getEmpId())
            .empName(empName)
            .empCode(empCode)

            .expectedStartDate(mapping.getExpectedStartDate())
            .expectedEndDate(mapping.getExpectedEndDate())
            .processingFrom(mapping.getProcessingFrom())
            .processingTo(mapping.getProcessingTo())
            .businessFrom(mapping.getBusinessFrom())
            .businessTo(mapping.getBusinessTo())
            .status(mapping.getStatus())
            .createdBy(mapping.getCreatedBy())
            .creationTime(mapping.getCreationTime())
            .updatedBy(mapping.getUpdatedBy())
            .updationTime(mapping.getUpdationTime())
            .build();
    }

    private HierarchyEmployeeDTO convertToHierarchyEmployeeDTO(EmployeeBasicDetail emp, int level) {
        String reportingManagerName = null;
        if (emp.getReportingManagerId() != null) {
            EmployeeBasicDetail manager = userCacheService.getUserById(emp.getReportingManagerId());
            if (manager != null) {
                reportingManagerName = manager.getName();
            }
        }

        return HierarchyEmployeeDTO.builder()
            .empId(emp.getId())
            .empName(emp.getName())
            .empCode(emp.getEmployeeCode())
            .designation(emp.getDesignation())
            .contactNumber(emp.getContactNumber())
            .emailId(emp.getEmailId())
            .status(emp.getStatus().name())
            .reportingManagerId(emp.getReportingManagerId())
            .reportingManagerName(reportingManagerName)
            .departmentId(emp.getDepartmentId())
            .departmentName(emp.getDepartmentName())
            .level(level)
            .subordinates(new ArrayList<>())
            .build();
    }

    /**
     * Build default metadata response when there are errors or missing data
     * @return Default metadata with all flags set to false
     */
    private RosteringMetadataResponseDTO buildDefaultMetadata() {
        return RosteringMetadataResponseDTO.builder()
            .allShiftManagement(false)
            .unitShiftManagement(false)
            .liveDashboardView(false)
            .shiftDashboardView(false)
            .employeeDashboardView(false)
            .build();
    }
}
