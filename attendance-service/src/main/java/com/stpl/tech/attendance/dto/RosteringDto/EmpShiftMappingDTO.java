package com.stpl.tech.attendance.dto.RosteringDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmpShiftMappingDTO {
    private int id;
    private int shiftId;
    private String shiftName;
    private int empId;
    private String empName;
    private String empCode;
    private Timestamp expectedStartDate;
    private Timestamp expectedEndDate;
    private Timestamp processingFrom;
    private Timestamp processingTo;
    private Timestamp businessFrom;
    private Timestamp businessTo;
    private String status;
    private String createdBy;
    private LocalDateTime creationTime;  // Keep as LocalDateTime to match JPA entity
    private String updatedBy;
    private Timestamp updationTime;
}
