package com.stpl.tech.attendance.service;

import com.stpl.tech.attendance.dto.DevicePairingRequest;
import com.stpl.tech.attendance.dto.DevicePairingResponse;
import com.stpl.tech.attendance.dto.EmployeeDetailsDTO;
import com.stpl.tech.attendance.dto.EmployeeMetadataDTO;
import com.stpl.tech.attendance.dto.AttendanceConfigDTO;
import com.stpl.tech.attendance.dto.UnitEligibilityDTO;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface MetadataService {
    Page<EmployeeMetadataDTO> getEmployeeMetadata(String searchTerm, int page, int size);
    DevicePairingResponse pairDevice(DevicePairingRequest request);

    /**
     * Validates if a device is paired with a specific unit
     * @param deviceId The ID of the device to check
     * @param unitId The ID of the unit to validate against
     * @return true if the device is paired with the unit, false otherwise
     */
    boolean isDevicePairedWithUnit(String deviceId, Long unitId);

    /**
     * Get employees who have registered biometrics
     * @param searchTerm Optional search term to filter employees
     * @param page Page number (0-based)
     * @param size Page size
     * @return Page of employees with registered biometrics
     */
    Page<EmployeeMetadataDTO> getEmployeesWithBiometric(String searchTerm, int page, int size);

    @Transactional(readOnly = true)
    List<EmployeeMetadataDTO> getEmployeeHierarchy(Integer empId);

    /**
     * Get employee metadata for a manager's team
     * @param empId The employee ID of the manager
     * @param searchTerm Optional search term to filter by employee code or name
     * @param page Page number (0-based)
     * @param size Page size
     * @return Page of employee metadata for the manager's team
     */
    Page<EmployeeMetadataDTO> getManagerTeamMetadata(Integer empId, String searchTerm, int page, int size);

    /**
     * Get all units for which an employee has attendance eligibility
     * @param empId The employee ID
     * @return List of units where the employee has attendance eligibility
     */
    List<UnitEligibilityDTO> getEmployeeAttendanceEligibleUnits(String empId);

    /**
     * Get employee details including biometric status and approval eligibility mapping
     * @param empId The employee ID
     * @return EmployeeDetailsDTO containing employee details
     */
    EmployeeDetailsDTO getEmployeeDetails(String empId);

    /**
     * Get attendance configuration properties for UI
     * @return AttendanceConfigDTO containing all attendance-related configuration
     */
    AttendanceConfigDTO getAttendanceConfig();
}