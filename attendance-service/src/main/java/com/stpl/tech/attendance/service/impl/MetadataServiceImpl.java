package com.stpl.tech.attendance.service.impl;

import com.stpl.tech.attendance.cache.service.UnitCacheService;
import com.stpl.tech.attendance.cache.service.UserCacheService;
import com.stpl.tech.attendance.config.EnvironmentProperties;
import com.stpl.tech.attendance.dto.BiometricRegistrationDTO;
import com.stpl.tech.attendance.dto.DevicePairingRequest;
import com.stpl.tech.attendance.dto.DevicePairingResponse;
import com.stpl.tech.attendance.dto.EmployeeDetailsDTO;
import com.stpl.tech.attendance.dto.EmployeeMetadataDTO;
import com.stpl.tech.attendance.dto.AttendanceConfigDTO;
import com.stpl.tech.attendance.dto.UnitEligibilityDTO;
import com.stpl.tech.attendance.entity.ApplicationInstallationData;
import com.stpl.tech.attendance.entity.BiometricRegistration;
import com.stpl.tech.attendance.entity.EmpEligibilityMapping;
import com.stpl.tech.attendance.enums.BiometricStatus;
import com.stpl.tech.attendance.enums.EligibilityType;
import com.stpl.tech.attendance.enums.MappingStatus;
import com.stpl.tech.attendance.enums.MappingType;
import com.stpl.tech.attendance.model.DeviceDetail;
import com.stpl.tech.attendance.model.DeviceDetailAudit;
import com.stpl.tech.attendance.model.TransferRequestStatus;
import com.stpl.tech.attendance.repository.ApplicationInstallationDataRepository;
import com.stpl.tech.attendance.repository.BiometricRegistrationRepository;
import com.stpl.tech.attendance.repository.DeviceDetailAuditRepository;
import com.stpl.tech.attendance.repository.DeviceDetailRepository;
import com.stpl.tech.attendance.repository.EmpEligibilityMappingRepository;
import com.stpl.tech.attendance.service.BiometricService;
import com.stpl.tech.attendance.service.MetadataService;
import com.stpl.tech.attendance.service.TransferService;
import com.stpl.tech.attendance.util.DateTimeUtil;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.util.AppConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class MetadataServiceImpl implements MetadataService {

    private final UserCacheService userCache;
    private final DeviceDetailRepository deviceDetailRepository;
    private final DeviceDetailAuditRepository deviceDetailAuditRepository;
    private final ApplicationInstallationDataRepository applicationInstallationDataRepository;
    private final BiometricRegistrationRepository biometricRegistrationRepository;
    private final EnvironmentProperties environmentProperties;
    private final UnitCacheService unitCacheService;
    private final EmployeeSearchService employeeSearchService;
    private final BiometricService biometricService;
    private final EmpEligibilityMappingRepository empEligibilityMappingRepository;
    private final TransferService transferService;

    @Override
    public Page<EmployeeMetadataDTO> getEmployeeMetadata(String searchTerm, int page, int size) {
        List<EmployeeBasicDetail> activeEmployees = employeeSearchService.searchEmployees(searchTerm);
        // Apply pagination
        int start = page * size;
        int end = Math.min(start + size, activeEmployees.size());

        List<EmployeeMetadataDTO> pagedResults = activeEmployees.subList(start, end).stream()
                .map(this::mapToEmployeeMetadataDTO)
                .collect(Collectors.toList());

        return new PageImpl<>(pagedResults, PageRequest.of(page, size), activeEmployees.size());
    }

    private EmployeeMetadataDTO mapToEmployeeMetadataDTO(EmployeeBasicDetail employee) {
        Optional<BiometricRegistrationDTO> biometricRegistration = getBiometricRegistration(employee.getId());
        BiometricRegistrationDTO registration = biometricRegistration.orElse(null);

        // Get the latest transfer status for the employee
        TransferRequestStatus transferStatus = transferService.getLatestTransferStatus(String.valueOf(employee.getId()))
                .orElse(TransferRequestStatus.NOT_FOUND);

        return EmployeeMetadataDTO.builder()
                .empId(employee.getId())
                .empCode(employee.getEmployeeCode())
                .empName(employee.getName())
                .designation(employee.getDesignation())
                .companyId(employee.getCompanyId())
                .biometricRegistrationStatus(biometricRegistration.isPresent() ? biometricRegistration.get().getStatus() :
                        BiometricStatus.NOT_FOUND)
                .transferRequestStatus(transferStatus)
                .registrationDate(registration!=null ? registration.getUpdatedAt() : null)
                .registrationDeviceId(registration!=null ? registration.getDeviceId() : null)
                .status(employee.getStatus().name())
                .imageUrl(registration != null ? registration.getImageUrl() : null)
                .registrationImageUrl(environmentProperties.getEmployeeImageUrl() + employee.getImagekey())
                .build();
    }

    private Optional<BiometricRegistrationDTO> getBiometricRegistration(Integer employeeId) {
        /*return biometricRegistrationRepository.findByEmpIdAndStatusIn(employeeId.toString(),new ArrayList<>(Arrays.asList(BiometricStatus.APPROVED,
                BiometricStatus.PENDING)));*/
        return Optional.ofNullable(biometricService.getBiometricRegistration(String.valueOf(employeeId)));
    }

    @Override
    @Transactional
    public DevicePairingResponse pairDevice(DevicePairingRequest request) {
        // Check if device exists
        Optional<DeviceDetail> existingDevice = deviceDetailRepository.findByDeviceId(request.getDeviceId());
        
        if (existingDevice.isPresent()) {
            DeviceDetail deviceDetail = existingDevice.get();
            
            // If device is already paired with the same unit, return success
            if (deviceDetail.getUnitId().equalsIgnoreCase(request.getUnitId().toString())) {
                return DevicePairingResponse.builder()
                    .success(true)
                    .message("Device is already paired with this unit")
                    .existingPairing(false)
                        .existingUnitId(deviceDetail.getUnitId())
                        .existingUnitName(unitCacheService.getUnitBasicDetail(Integer.parseInt(deviceDetail.getUnitId())).getName())
                    .build();
            }
            
            // If device is paired with a different unit and override is not requested
            if (!request.isOverrideExisting()) {
                return DevicePairingResponse.builder()
                    .success(false)
                    .message("Device is already paired with another unit.")
                    .existingPairing(true)
                    .existingUnitId(deviceDetail.getUnitId())
                        .existingUnitName(unitCacheService.getUnitBasicDetail(Integer.parseInt(deviceDetail.getUnitId())).getName())
                    .build();
            }

            // Create audit log
            DeviceDetailAudit audit = DeviceDetailAudit.builder()
                    .deviceId(deviceDetail.getDeviceId())
                    .oldUnitId(deviceDetail.getUnitId())
                    .newUnitId(request.getUnitId().toString())
                    .action("OVERRIDE")
                    .timestamp(DateTimeUtil.now())
                    .build();


            // Handle override case
            deviceDetail.setLastUpdatedAt(DateTimeUtil.now());
            deviceDetail.setUnitId(request.getUnitId().toString());

            deviceDetailAuditRepository.save(audit);
            
            deviceDetailRepository.save(deviceDetail);
        } else {
            // Create new device
            DeviceDetail deviceDetail = DeviceDetail.builder()
                    .deviceId(request.getDeviceId())
                    .os(request.getOs())
                    .version(request.getVersion())
                    .unitId(request.getUnitId().toString())
                    .createdAt(DateTimeUtil.now())
                    .lastUpdatedAt(DateTimeUtil.now())
                    .build();
            deviceDetailRepository.save(deviceDetail);
        }

        // Handle application installation data
        Optional<ApplicationInstallationData> existingInstallation =
            applicationInstallationDataRepository.findByMachineIdAndStatus(
                request.getDeviceId(), 
                AppConstants.ACTIVE
            );

        if (existingInstallation.isPresent()) {
            ApplicationInstallationData installation = existingInstallation.get();
            if (!installation.getUnitId().equals(request.getUnitId().intValue())) {
                // Deactivate old installation
                installation.setStatus(AppConstants.IN_ACTIVE);
                applicationInstallationDataRepository.save(installation);

                // Create new installation
                createNewInstallation(request);
            }
        } else {
            createNewInstallation(request);
        }

        return DevicePairingResponse.builder()
            .success(true)
            .message("Device paired successfully")
            .existingPairing(false)
            .build();
    }

    private void createNewInstallation(DevicePairingRequest request) {
        Optional<ApplicationInstallationData> existingInstallation =
                applicationInstallationDataRepository.findByMachineId(
                        request.getDeviceId()
                );
        if(existingInstallation.isPresent()){
            existingInstallation.get().setUnitId(request.getUnitId().intValue());
            existingInstallation.get().setUpdatedDate(DateTimeUtil.now());
            existingInstallation.get().setUpdatedBy(request.getPairedBy().toString());
            existingInstallation.get().setStatus(AppConstants.ACTIVE);
            applicationInstallationDataRepository.save(existingInstallation.get());
        }else{
            ApplicationInstallationData newInstallation = ApplicationInstallationData.builder()
                    .machineId(request.getDeviceId())
                    .unitId(request.getUnitId().intValue())
                    .createdDate(DateTimeUtil.now())
                    .createdBy(request.getPairedBy().toString())
                    .osVersion(request.getOs())
                    .status(AppConstants.ACTIVE)
                    .build();
            applicationInstallationDataRepository.save(newInstallation);
        }
    }

    @Override
    public boolean isDevicePairedWithUnit(String deviceId, Long unitId) {
        // Check if device exists and is paired with the given unit
        Optional<DeviceDetail> deviceDetail = deviceDetailRepository.findByDeviceId(deviceId);
        if (deviceDetail.isEmpty()) {
            return false;
        }

        // Check if device is paired with the given unit
        return deviceDetail.get().getUnitId().equals(unitId.toString());
    }

    @Override
    @Transactional(readOnly = true)
    public Page<EmployeeMetadataDTO> getEmployeesWithBiometric(String searchTerm, int page, int size) {
        List<EmployeeBasicDetail> allEmployees = userCache.getAllUserCache().values()
                .stream()
                .toList();

        // Filter active employees with biometric registration
        List<EmployeeBasicDetail> activeEmployees = allEmployees.stream()
                .filter(emp -> AppConstants.ACTIVE.equals(emp.getStatus().name()))
                .collect(Collectors.toList());

        // Get all employee IDs
        List<Integer> employeeIds = activeEmployees.stream()
                .map(EmployeeBasicDetail::getId)
                .collect(Collectors.toList());

        // Batch fetch all biometric registrations
        List<BiometricRegistration> biometricRegistrations = biometricRegistrationRepository
                .findByEmpIdInAndStatus(employeeIds.stream().map(String::valueOf).collect(Collectors.toList()), BiometricStatus.APPROVED);

        // Create a map of employee ID to biometric registration
        Map<String, BiometricRegistration> registrationMap = biometricRegistrations.stream()
                .collect(Collectors.toMap(
                    BiometricRegistration::getEmpId,
                    registration -> registration
                ));

        // Filter employees with approved biometric registration
        List<EmployeeBasicDetail> employeesWithBiometric = activeEmployees.stream()
                .filter(emp -> registrationMap.containsKey(String.valueOf(emp.getId())))
                .collect(Collectors.toList());

        // Apply search filter if provided
        if (org.springframework.util.StringUtils.hasText(searchTerm)) {
            employeesWithBiometric = employeesWithBiometric.stream()
                    .filter(emp ->
                            (emp.getEmployeeCode() != null && emp.getEmployeeCode().equals(searchTerm)) ||
                            emp.getName().toLowerCase().startsWith(searchTerm.toLowerCase()))
                    .toList();
        }

        // Apply pagination
        int start = page * size;
        int end = Math.min(start + size, employeesWithBiometric.size());

        List<EmployeeMetadataDTO> pagedResults = employeesWithBiometric.subList(start, end).stream()
                .map(this::mapToEmployeeMetadataDTO)
                .collect(Collectors.toList());

        return new PageImpl<>(pagedResults, PageRequest.of(page, size), employeesWithBiometric.size());
    }

    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = "employeeHierarchy", key = "#empId")
    public List<EmployeeMetadataDTO> getEmployeeHierarchy(Integer empId) {
        log.info("Fetching team metadata for manager with empId: {}", empId);

        try {
            // 1. Get the manager's details
            EmployeeBasicDetail manager = userCache.getUserById(empId);
            if (manager == null) {
                log.warn("Manager not found with empId: {}", empId);
                return new ArrayList<>();
            }
            Set<Integer> employeeIds = new HashSet<>();
            LocalDate currentDate = LocalDate.now();

            // 2. Get all units where this employee is a manager
            Set<Integer> managedUnitIds = new HashSet<>();
            unitCacheService.getAllUnitBasicDetail().forEach(unit -> {
                if (empId.equals(unit.getUnitManagerId()) || empId.equals(unit.getCafeManagerId()) || empId.equals(unit.getUnitCafeManager())) {
                    managedUnitIds.add(unit.getId());
                }
            });


            // 3. Get all employees with active attendance eligibility mappings for these units
            List<EmpEligibilityMapping> eligibilityMappings = empEligibilityMappingRepository
                    .findByValueInAndMappingTypeAndEligibilityTypeAndStatus(
                            managedUnitIds.stream().map(String::valueOf).collect(Collectors.toList()),
                            MappingType.UNIT,
                            EligibilityType.ATTENDANCE,
                            MappingStatus.ACTIVE
                    ).stream().filter(mapping -> isMappingActive(mapping, currentDate)).toList();

            // 4. Get unique employee IDs from the mappings
            employeeIds.addAll(eligibilityMappings.stream()
                    .map(EmpEligibilityMapping::getEmpId)
                    .map(Integer::parseInt)
                    .collect(Collectors.toSet()));

            // 5. Fetch employee details and create DTOs
            return employeeIds.stream()
                    .map(id -> {
                        EmployeeBasicDetail employee = userCache.getUserById(id);
                        if (employee != null) {
                            return mapToEmployeeMetadataDTO(employee);
                        }
                        return null;
                    })
                    .filter(Objects::nonNull)
                    .toList();


        } catch (Exception e) {
            log.error("Error fetching employee hierarchy for empId: {}", empId, e);
            // If there's a cache serialization issue, evict the cache and retry
            if (e.getCause() instanceof org.springframework.data.redis.serializer.SerializationException) {
                log.warn("Cache serialization issue detected, evicting cache for empId: {}", empId);
                evictEmployeeHierarchyCache(empId);
            }
            return new ArrayList<>();
        }
    }

    /**
     * Evict the employee hierarchy cache for a specific employee
     */
    @CacheEvict(value = "employeeHierarchy", key = "#empId")
    public void evictEmployeeHierarchyCache(Integer empId) {
        log.debug("Evicting employee hierarchy cache for empId: {}", empId);
    }


    @Override
    @Transactional(readOnly = true)
    public Page<EmployeeMetadataDTO> getManagerTeamMetadata(Integer empId, String searchTerm, int page, int size) {
        log.info("Fetching team metadata for manager with empId: {}", empId);

        List<EmployeeMetadataDTO> employeeMetadata =  getEmployeeHierarchy(empId);
        if (employeeMetadata.isEmpty()) {
            log.info("No units found where employee {} is a manager", empId);
            EmployeeBasicDetail employee = userCache.getUserById(empId);
            if (employee != null) {
                employeeMetadata.add(mapToEmployeeMetadataDTO(employee));
            }
        }
        // 6. Apply search filter if provided
        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            String searchTermLower = searchTerm.toLowerCase().trim();
            employeeMetadata = employeeMetadata.stream()
                .filter(emp ->
                    (emp.getEmpCode() != null && emp.getEmpCode().toLowerCase().equalsIgnoreCase(searchTermLower)) ||
                    (emp.getEmpName() != null && emp.getEmpName().toLowerCase().startsWith(searchTermLower))
                )
                .collect(Collectors.toList());
        }

        // 7. Apply pagination
        int start = page * size;
        int end = Math.min(start + size, employeeMetadata.size());
        List<EmployeeMetadataDTO> pagedResults = start < employeeMetadata.size()
            ? employeeMetadata.subList(start, end)
            : Collections.emptyList();

        log.info("Found {} employees in manager's team after search", employeeMetadata.size());
        return new PageImpl<>(pagedResults, PageRequest.of(page, size), employeeMetadata.size());
    }

    @Override
    @Transactional(readOnly = true)
    public List<UnitEligibilityDTO> getEmployeeAttendanceEligibleUnits(String empId) {
        log.info("Fetching attendance eligible units for employee: {}", empId);

        // Get all active attendance eligibility mappings for the employee
        List<EmpEligibilityMapping> eligibilityMappings = empEligibilityMappingRepository
            .findByEmpIdAndEligibilityTypeAndStatus(
                empId,
                EligibilityType.ATTENDANCE,
                MappingStatus.ACTIVE
            );

        // Filter mappings that are for units and are currently active (within date range)
        LocalDate currentDate = LocalDate.now();
        log.info("Current date: {}", currentDate);
        List<EmpEligibilityMapping> activeUnitMappings = eligibilityMappings.stream()
            .filter(mapping -> mapping.getMappingType() == MappingType.UNIT)
            .filter(mapping -> isMappingActive(mapping, currentDate))
            .collect(Collectors.toList());

        // Convert to DTOs with unit details
        List<UnitEligibilityDTO> unitEligibilities = activeUnitMappings.stream()
            .map(mapping -> {
                try {
                    Integer unitId = Integer.parseInt(mapping.getValue());
                    com.stpl.tech.master.domain.model.UnitBasicDetail unitDetail = unitCacheService.getUnitBasicDetail(unitId);

                    if (unitDetail != null) {
                        return UnitEligibilityDTO.builder()
                            .unitId(unitId)
                            .unitName(unitDetail.getName())
                            .unitCode(unitDetail.getReferenceName())
                            .startDate(mapping.getStartDate())
                            .endDate(mapping.getEndDate())
                            .status(mapping.getStatus().name())
                            .build();
                    } else {
                        log.warn("Unit details not found for unitId: {}", unitId);
                        return null;
                    }
                } catch (NumberFormatException e) {
                    log.error("Invalid unit ID format in mapping: {}", mapping.getValue());
                    return null;
                }
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        log.info("Found {} eligible units for employee: {}", unitEligibilities.size(), empId);
        return unitEligibilities;
    }

    @Override
    @Transactional(readOnly = true)
    public EmployeeDetailsDTO getEmployeeDetails(String empId) {
        log.info("Getting employee details for employee: {}", empId);

        // Get employee basic details from cache
        EmployeeBasicDetail employee = userCache.getUserById(Integer.parseInt(empId));
        if (employee == null) {
            log.warn("Employee not found with empId: {}", empId);
            throw new RuntimeException("Employee not found");
        }

        // Get biometric registration status
        BiometricStatus biometricStatus = BiometricStatus.NOT_FOUND;
        Optional<BiometricRegistrationDTO> biometricRegistration = getBiometricRegistration(employee.getId());
        if (biometricRegistration.isPresent()) {
            biometricStatus = biometricRegistration.get().getStatus();
        }

        // Check if employee has approval eligibility mapping
        boolean hasApprovalEligibilityMapping = checkApprovalEligibilityMapping(empId);

        return EmployeeDetailsDTO.builder()
                .empId(empId)
                .empName(employee.getName())
                .empCode(employee.getEmployeeCode())
                .empContact(employee.getContactNumber())
                .empEmail(employee.getEmailId())
                .biometricRegistrationStatus(biometricStatus)
                .hasApprovalEligibilityMapping(hasApprovalEligibilityMapping)
                .build();
    }

    /**
     * Check if employee has any approval eligibility mapping
     */
    private boolean checkApprovalEligibilityMapping(String empId) {
        List<EmpEligibilityMapping> approvalMappings = empEligibilityMappingRepository
            .findByEmpIdAndEligibilityTypeAndStatus(
                empId,
                EligibilityType.APPROVAL,
                MappingStatus.ACTIVE
            );

        // Check if any approval mappings exist and are currently active
        LocalDate currentDate = LocalDate.now();
        return approvalMappings.stream()
            .anyMatch(mapping -> isMappingActive(mapping, currentDate));
    }

    /**
     * Utility method to check if a mapping is currently active based on date range
     * Compares only the date part, ignoring any time components
     */
    private boolean isMappingActive(EmpEligibilityMapping mapping, LocalDate currentDate) {
        // Check start date: mapping is active if start date is null, before current date, or equal to current date
        boolean startDateValid = mapping.getStartDate() == null ||
                                mapping.getStartDate().isBefore(currentDate) ||
                                mapping.getStartDate().isEqual(currentDate);

        // Check end date: mapping is active if end date is null or after current date
        boolean endDateValid = mapping.getEndDate() == null ||
                              mapping.getEndDate().isAfter(currentDate);

        return startDateValid && endDateValid;
    }

    @Override
    public AttendanceConfigDTO getAttendanceConfig() {
        return AttendanceConfigDTO.builder()
                .attendanceRegistrationIsLivelinessCheck(environmentProperties.isLivelinessCheckEnabledForRegistration())
                .attendanceRegistrationStartAngle(environmentProperties.getStartAngleForAttendanceRegistration())
                .attendanceRegistrationEndAngle(environmentProperties.getEndAngleForAttendanceRegistration())
                .attendanceRegistrationAngleChangeInterval(environmentProperties.getAngleChangeIntervalForAttendanceRegistration())
                .attendanceRegistrationAngleChangeThreshold(environmentProperties.getAngleChangeThresholdForAttendanceRegistration())
                .attendanceRegistrationLivelinessChallenges(environmentProperties.getLivelinessChallengesForAttendanceRegistration())
                .attendancePunchinIsLivelinessCheck(environmentProperties.isLivelinessCheckEnabledForPunchIn())
                .attendancePunchinStartAngle(environmentProperties.getStartAngleForAttendancePunchIn())
                .attendancePunchinEndAngle(environmentProperties.getEndAngleForAttendancePunchIn())
                .attendancePunchinAngleChangeInterval(environmentProperties.getAngleChangeIntervalForAttendancePunchIn())
                .attendancePunchinAngleChangeThreshold(environmentProperties.getAngleChangeThresholdForAttendancePunchIn())
                .attendancePunchinLivelinessChallenges(environmentProperties.getLivelinessChallengesForAttendancePunchIn())
                .attendanceAdminDefaultContact(environmentProperties.getAttendanceAdminDefaultContact())
                .attendanceAdminDefaultEmail(environmentProperties.getAttendanceAdminDefaultEmail())
                .attendancePunchinRotationThreshold(environmentProperties.getAttendancePunchInRotationThreshold())
                .attendancePunchinBrightnessThreshold(environmentProperties.getAttendancePunchInBrightnessThreshold())
                .attendanceRegistrationBrightnessThreshold(environmentProperties.getAttendanceRegistrationBrightnessThreshold())
                .attendanceRegistrationRotationThreshold(environmentProperties.getAttendanceRegistrationRotationThreshold())
                .attendancePunchinContrastThreshold(environmentProperties.getAttendancePunchInContrastThreshold())
                .attendanceRegistrationContrastThreshold(environmentProperties.getAttendanceRegistrationContrastThreshold())
                .build();
    }

}