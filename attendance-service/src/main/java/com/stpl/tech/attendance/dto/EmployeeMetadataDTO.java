package com.stpl.tech.attendance.dto;

import com.stpl.tech.attendance.enums.BiometricRegistrationStatus;
import com.stpl.tech.attendance.enums.BiometricStatus;
import com.stpl.tech.attendance.model.TransferRequestStatus;
import lombok.Builder;
import lombok.Data;
import org.springframework.data.redis.core.RedisHash;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
public class EmployeeMetadataDTO implements Serializable {
    private Integer empId;
    private String empCode;
    private String empName;
    private String designation;
    private Integer companyId;
    private BiometricStatus biometricRegistrationStatus;
    private TransferRequestStatus transferRequestStatus;
    private LocalDateTime registrationDate;
    private String registrationDeviceId;
    private String status;
    private String imageUrl;
    private String registrationImageUrl;
} 