package com.stpl.tech.attendance.dto.RosteringDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmpShiftUpdateResponseDTO {
    private Boolean success;
    private String message;
    private Integer updatedShifts;
    private Integer updatedEmployee;
    private Integer totalUpdatedMappings;
}
