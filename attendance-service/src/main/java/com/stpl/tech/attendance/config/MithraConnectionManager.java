package com.stpl.tech.attendance.config;

import com.gs.fw.common.mithra.connectionmanager.SourcelessConnectionManager;
import com.gs.fw.common.mithra.databasetype.DatabaseType;
import com.gs.fw.common.mithra.databasetype.MariaDatabaseType;
import com.gs.fw.common.mithra.bulkloader.BulkLoader;
import com.gs.fw.common.mithra.bulkloader.BulkLoaderException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import java.sql.Connection;
import java.sql.DriverManager;
import java.util.TimeZone;

@Component
public class MithraConnectionManager implements SourcelessConnectionManager, ApplicationContextAware {

    private static ApplicationContext applicationContext;

    @Value("${spring.datasource.url}")
    private String databaseUrl;

    @Value("${spring.datasource.username}")
    private String databaseUsername;

    @Value("${spring.datasource.password}")
    private String databasePassword;

    @Value("${spring.datasource.driver-class-name}")
    private String driverClassName;

    public static MithraConnectionManager getInstance() {
        if (applicationContext == null) {
            throw new RuntimeException("ApplicationContext not initialized. Cannot get MithraConnectionManager instance.");
        }
        return applicationContext.getBean(MithraConnectionManager.class);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        MithraConnectionManager.applicationContext = applicationContext;
    }

    @Override
    public Connection getConnection() {
        try {
            Class.forName(driverClassName);
            return DriverManager.getConnection(databaseUrl, databaseUsername, databasePassword);
        } catch (Exception e) {
            throw new RuntimeException("Failed to get database connection", e);
        }
    }

    @Override
    public DatabaseType getDatabaseType() {
        return MariaDatabaseType.getInstance();
    }

    @Override
    public BulkLoader createBulkLoader() throws BulkLoaderException {
        throw new BulkLoaderException("Bulk loading not supported");
    }

    @Override
    public TimeZone getDatabaseTimeZone() {
        return TimeZone.getDefault();
    }

    @Override
    public String getDatabaseIdentifier() {
        return "default";
    }
}