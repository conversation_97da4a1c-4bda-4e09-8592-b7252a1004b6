package com.stpl.tech.attendance.dto.RosteringDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RosteringMetadataResponseDTO {
    
    /**
     * Flag to enable/disable all shift management features
     */
    private Boolean allShiftManagement;
    
    /**
     * Flag to enable/disable unit-specific shift management features
     */
    private Boolean unitShiftManagement;
    
    /**
     * Flag to enable/disable live dashboard view
     */
    private Boolean liveDashboardView;
    
    /**
     * Flag to enable/disable shift dashboard view
     */
    private Boolean shiftDashboardView;
    
    /**
     * Flag to enable/disable employee dashboard view
     */
    private Boolean employeeDashboardView;
} 