package com.stpl.tech.attendance.dto.RosteringDto;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmpShiftUpdateRequestDTO {

    @NotNull(message = "Employee ID is required")
    private Integer empId;           // ← Single employee

    @NotNull(message = "Shift ID is required")
    private Integer shiftId;         // ← Single shift

    @NotNull(message = "Business from date is required")
    private LocalDateTime businessFrom;     // ← When the fact becomes true

    private LocalDateTime businessTo;       // ← When the fact ends

    private Boolean updateUpcomingShifts = true;  // ← Flag for future updates

    private LocalDateTime expectedArrivalTime;    // ← Expected arrival time

    private String updatedBy;  // ← Will be set from JWT context in controller
}
