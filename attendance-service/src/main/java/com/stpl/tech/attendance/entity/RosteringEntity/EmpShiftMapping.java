package com.stpl.tech.attendance.entity.RosteringEntity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.sql.Time;
import java.sql.Timestamp;
import java.time.LocalDateTime;

@Entity
@Table(name = "EMP_SHIFT_MAPPING")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmpShiftMapping {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private int id;

    @Column(name = "SHIFT_ID", nullable = false)
    private int shiftId;

    @Column(name = "EMP_ID", nullable = false)
    private int empId;

    @Column(name = "EXPECTED_START_DATE")
    private Timestamp expectedStartDate;

    @Column(name = "EXPECTED_END_DATE")
    private Timestamp expectedEndDate;

    @Column(name = "PROCESSING_FROM")
    private Timestamp processingFrom;

    @Column(name = "PROCESSING_TO")
    private Timestamp processingTo;

    @Column(name = "BUSINESS_FROM")
    private Timestamp businessFrom;

    @Column(name = "BUSINESS_TO")
    private Timestamp businessTo;

    @Column(name = "STATUS", length = 45, nullable = false)
    private String status = "ACTIVE";

    @Column(name = "CREATED_BY", length = 100)
    private String createdBy;

    @CreationTimestamp
    @Column(name = "CREATION_TIME", nullable = false, updatable = false)
    private LocalDateTime creationTime;

    @Column(name = "UPDATED_BY", length = 100)
    private String updatedBy;

    @UpdateTimestamp
    @Column(name = "UPDATION_TIME", nullable = false)
    private Timestamp updationTime;

    // Foreign key relationship
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "SHIFT_ID", insertable = false, updatable = false)
    private Shift shift;
}
