package com.stpl.tech.attendance.controller;

import com.stpl.tech.attendance.constants.ApiConstants;
import com.stpl.tech.attendance.context.JwtContext;
import com.stpl.tech.attendance.dto.RosteringDto.CafeLiveDashboardResponseDTO;
import com.stpl.tech.attendance.dto.RosteringDto.CafeShiftDataDTO;
import com.stpl.tech.attendance.dto.RosteringDto.EmpShiftMappingDTO;
import com.stpl.tech.attendance.dto.RosteringDto.EmpShiftUpdateRequestDTO;
import com.stpl.tech.attendance.dto.RosteringDto.EmployeeShiftDataResponseDTO;
import com.stpl.tech.attendance.dto.RosteringDto.HierarchyEmployeeDTO;
import com.stpl.tech.attendance.dto.RosteringDto.ShiftCafeMappingDTO;
import com.stpl.tech.attendance.dto.RosteringDto.ShiftEmployeesResponseDTO;
import com.stpl.tech.attendance.dto.RosteringDto.ShiftRequestDTO;
import com.stpl.tech.attendance.dto.RosteringDto.ShiftResponseDTO;
import com.stpl.tech.attendance.dto.RosteringDto.ShiftEmployeesRequestDTO;
import com.stpl.tech.attendance.dto.RosteringDto.EmpShiftUpdateResponseDTO;
import com.stpl.tech.attendance.dto.RosteringDto.RosteringMetadataResponseDTO;
import com.stpl.tech.attendance.service.RosteringService.RosteringService;
import com.stpl.tech.attendance.service.RosteringService.ReladomoEmpShiftService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.sql.Timestamp;
import java.util.List;
import com.stpl.tech.attendance.model.response.ApiResponse;

// Reladomo classes are now available and can be used in services

@RestController
@RequestMapping(ApiConstants.Paths.ROSTER)
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Rostering", description = "Rostering management APIs")
public class RosteringController extends BaseController{

    private final RosteringService rosteringService;
    private final ReladomoEmpShiftService reladomoEmpShiftService;

    @GetMapping("/get-rostering-metadata")
    @Operation(summary = "Get rostering metadata", description = "Get metadata flags for rostering features")
    public ResponseEntity<ApiResponse<RosteringMetadataResponseDTO>> getRosteringMetadata() {
        Integer employeeId = JwtContext.getInstance().getUserId();
        Integer unitId = JwtContext.getInstance().getUnitId();
        log.info("Getting rostering metadata for employeeId: {}, unitId: {}", employeeId, unitId);
        RosteringMetadataResponseDTO metadata = rosteringService.getRosteringMetadata(employeeId, unitId);
        return success(metadata);
    }

    @GetMapping("/dashboard/cafe-live-dashboard")
    @Operation(summary = "Get cafe live dashboard", description = "Get live dashboard data based on employee ID")
    public ResponseEntity<ApiResponse<CafeLiveDashboardResponseDTO>> getCafeLiveDashboard() {
        Integer employeeId = JwtContext.getInstance().getUserId();
        log.info("Getting cafe live dashboard for Employee : {}", employeeId);
        CafeLiveDashboardResponseDTO dashboard = rosteringService.getCafeLiveDashboard(employeeId);
        return success(dashboard);
    }

    @PostMapping("/shifts/shift-employees")
    @Operation(summary = "Get shift employees", description = "Get employees assigned to shifts")
    public ResponseEntity<ApiResponse<ShiftEmployeesResponseDTO>> getShiftEmployees(@Valid @RequestBody ShiftEmployeesRequestDTO requestDTO) {
        log.info("Getting shift employees for shiftIds: {}, date: {}", requestDTO.getShiftIds(), requestDTO.getDate());
        ShiftEmployeesResponseDTO response = rosteringService.getShiftEmployeesForUser(requestDTO.getShiftIds(), requestDTO.getDate(),
                JwtContext.getInstance().getUserId());
        return success(response);
    }

    @GetMapping("/shifts/emp-shift-data/{empId}")
    @Operation(summary = "Get employee shift data", description = "Get shift assignments for a specific employee")
    public ResponseEntity<ApiResponse<EmployeeShiftDataResponseDTO>> getEmpUpcomingShiftData(
            @Parameter(description = "Employee ID", required = true)
            @PathVariable Integer empId,
            @Parameter(description = "Start time filter (optional)")
            @RequestParam(required = false)
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) Timestamp startDate,
            @Parameter(description = "End time filter (optional)")
            @RequestParam(required = false)
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) Timestamp endDate) {
        log.info("Getting employee shift data for empId: {}, startDate: {}, endDate: {}", empId, startDate, endDate);
        EmployeeShiftDataResponseDTO response = rosteringService.getEmpUpcomingShiftData(empId, startDate, endDate);
        return success(response);
    }

    @PutMapping("/shifts/emp-shift-update")
    @Operation(summary = "Update employee shifts with Reladomo", description = "Update employee shift assignments using Reladomo with bitemporal support")
    public ResponseEntity<ApiResponse<EmpShiftUpdateResponseDTO>> updateEmpShift(
            @Parameter(description = "Employee shift update request", required = true)
            @Valid @RequestBody EmpShiftUpdateRequestDTO request) {
        log.info("Updating employee shift mapping with Reladomo: empId={}, shiftId={}", 
                request.getEmpId(), request.getShiftId());
        
        // Set the updatedBy field from JWT context
        request.setUpdatedBy(String.valueOf(JwtContext.getInstance().getUserId()));
        
        EmpShiftUpdateResponseDTO result = reladomoEmpShiftService.updateEmpShifts(request);
        return success(result);
    }

    @GetMapping("/shifts/cafe-shift-data")
    @Operation(summary = "Get cafe shift data", description = "Get shift data for cafes/units")
    public ResponseEntity<ApiResponse<List<CafeShiftDataDTO>>> getCafeShiftData() {
        Integer employeeId = JwtContext.getInstance().getUserId();
        Integer unitId = JwtContext.getInstance().getUnitId();
        log.info("Getting cafe shift data for employeeId: {}, unitId: {}", employeeId, unitId);
        List<CafeShiftDataDTO> shiftData = rosteringService.getCafeShiftData(employeeId, unitId);
        return success(shiftData);
    }

    @GetMapping("/employees/get-hierarchy-employees")
    @Operation(summary = "Get hierarchy employees", description = "Get employee hierarchy data")
    public ResponseEntity<ApiResponse<List<HierarchyEmployeeDTO>>> getHierarchyEmployees() {
        Integer employeeId = JwtContext.getInstance().getUserId();
        log.info("Getting hierarchy employees for employeeId: {}", employeeId);
        List<HierarchyEmployeeDTO> employees = rosteringService.getHierarchyEmployees(employeeId);
        return success(employees);
    }


    @PostMapping("/shifts")
    @Operation(summary = "Create shift", description = "Create a new shift")
    public ResponseEntity<ApiResponse<ShiftResponseDTO>> createShift(
            @Parameter(description = "Shift data", required = true)
            @Valid @RequestBody ShiftRequestDTO shiftRequestDTO) {
        Integer employeeId = JwtContext.getInstance().getUserId();
        log.info("Creating new shift: {}", shiftRequestDTO.getShiftName());
        ShiftResponseDTO created = rosteringService.createShift(shiftRequestDTO, String.valueOf(employeeId));
        return success(created);
    }






    // Additional CRUD endpoints for shifts

    @PutMapping("/shifts/{shiftId}")
    @Operation(summary = "Update shift", description = "Update an existing shift")
    public ResponseEntity<ApiResponse<ShiftResponseDTO>> updateShift(
            @Parameter(description = "Shift ID", required = true)
            @PathVariable Integer shiftId,
            @Parameter(description = "Updated shift data", required = true)
            @Valid @RequestBody ShiftRequestDTO shiftRequestDTO) {
        Integer employeeId = JwtContext.getInstance().getUserId();
        log.info("Updating shift with ID: {}", shiftId);
        ShiftResponseDTO updated = rosteringService.updateShift(shiftId, shiftRequestDTO, String.valueOf(employeeId));
        return success(updated);
    }

    @GetMapping("/shifts")
    @Operation(summary = "Get all shifts", description = "Get all shifts with optional status filter")
    public ResponseEntity<ApiResponse<List<ShiftResponseDTO>>> getAllShifts(
            @Parameter(description = "Status filter (optional)")
            @RequestParam(required = false) String status) {
        log.info("Getting all shifts with status: {}", status);
        List<ShiftResponseDTO> shifts = rosteringService.getAllShifts(status);
        return success(shifts);
    }

    // Shift-Cafe mapping endpoints

    @PostMapping("/shifts/{shiftId}/units/{unitId}")
    @Operation(summary = "Create shift-cafe mapping", description = "Map a shift to a cafe/unit")
    public ResponseEntity<ApiResponse<ShiftCafeMappingDTO>> createShiftCafeMapping(
            @Parameter(description = "Shift ID", required = true)
            @PathVariable Integer shiftId,
            @Parameter(description = "Unit ID", required = true)
            @PathVariable Integer unitId) {
        Integer employeeId = JwtContext.getInstance().getUserId();
        log.info("Creating shift cafe mapping for shiftId: {}, unitId: {}", shiftId, unitId);
        ShiftCafeMappingDTO mapping = rosteringService.createShiftCafeMapping(shiftId, unitId, String.valueOf(employeeId));
        return success(mapping);
    }

    @DeleteMapping("/shifts/{shiftId}/units/{unitId}")
    @Operation(summary = "Delete shift-cafe mapping", description = "Remove mapping between shift and cafe/unit")
    public ResponseEntity<ApiResponse<Void>> deleteShiftCafeMapping(
            @Parameter(description = "Shift ID", required = true)
            @PathVariable Integer shiftId,
            @Parameter(description = "Unit ID", required = true)
            @PathVariable Integer unitId) {
        log.info("Deleting shift cafe mapping for shiftId: {}, unitId: {}", shiftId, unitId);
        rosteringService.deleteShiftCafeMapping(shiftId, unitId);
        return this.noContent();
    }

    // Employee-Shift mapping endpoints

    @PostMapping("/employees/shift-mapping")
    @Operation(summary = "Create employee shift mapping", description = "Assign an employee to a shift")
    public ResponseEntity<ApiResponse<EmpShiftMappingDTO>> createEmpShiftMapping(
            @Parameter(description = "Employee shift mapping request", required = true)
            @Valid @RequestBody EmpShiftUpdateRequestDTO request) {
        log.info("Creating employee shift mapping: {}", request);
        EmpShiftMappingDTO mapping = rosteringService.createEmpShiftMapping(request);
        return success(mapping);
    }

    @DeleteMapping("/employees/shift-mapping/{mappingId}")
    @Operation(summary = "Delete employee shift mapping", description = "Remove employee from shift assignment")
    public ResponseEntity<ApiResponse<Void>> deleteEmpShiftMapping(
            @Parameter(description = "Mapping ID", required = true)
            @PathVariable Integer mappingId) {
        log.info("Deleting employee shift mapping with ID: {}", mappingId);
        rosteringService.deleteEmpShiftMapping(mappingId);
        return this.noContent();
    }
}
